﻿/*
 * @Author: liquan
 * @Date: 2024-03-29 14:46:32
 * @Last Modified by: liquan
 * @Last Modified time: 2024-03-29 18:10:59
 */

#include "JlCompressEx.h"

#include <QDebug>

bool JlCompressEx::CompressToBuffer(QString file, const char *password, QIODevice &zipIoDevice)
{
    QuaZip zip(&zipIoDevice);
    // QDir().mkpath(QFileInfo(fileCompressed).absolutePath());
    if (!zip.open(QuaZip::mdCreate))
    {
        return false;
    }
    // Aggiungo il file
    if (!compressFileEx(&zip, file, QFileInfo(file).fileName(), password))
    {
        return false;
    }

    // Chiudo il file zip
    zip.close();
    if (zip.getZipError() != UNZ_OK)
    {
        return false;
    }

    return true;
}

bool JlCompressEx::CompressToBuffer(QByteArray &sourceData, QString fileName, const char *password, QIODevice &zipIoDevice)
{
    QuaZip zip(&zipIoDevice);
    // QDir().mkpath(QFileInfo(fileCompressed).absolutePath());
    if (!zip.open(QuaZip::mdCreate))
    {
        return false;
    }

    if (!CompressBuffer(zip, sourceData, fileName, password))
    {
        return false;
    }

    // Chiudo il file zip
    zip.close();
    if (zip.getZipError() != UNZ_OK)
    {
        return false;
    }

    return true;
}

bool JlCompressEx::CompressToBuffer(QStringList files, const char *password, QIODevice &zipIoDevice)
{
    // Creo lo zip
    QuaZip zip(&zipIoDevice);
    // QDir().mkpath(QFileInfo(fileCompressed).absolutePath());
    if (!zip.open(QuaZip::mdCreate))
    {
        return false;
    }
    // Comprimo i file
    QFileInfo info;
    for (int index = 0; index < files.size(); ++index)
    {
        const QString &file(files.at(index));
        info.setFile(file);
        if (!info.exists() || !compressFileEx(&zip, file, info.fileName(), password))
        {
            return false;
        }
    }
    // Chiudo il file zip
    zip.close();
    if (zip.getZipError() != 0)
    {
        return false;
    }

    return true;
}

bool JlCompressEx::compressFileEx(QuaZip *zip, QString fileName, QString fileDest, const char *password)
{
    // zip: oggetto dove aggiungere il file
    // fileName: nome del file reale
    // fileDest: nome del file all'interno del file compresso

    // Controllo l'apertura dello zip
    if (!zip)
        return false;
    if (zip->getMode() != QuaZip::mdCreate &&
        zip->getMode() != QuaZip::mdAppend &&
        zip->getMode() != QuaZip::mdAdd)
        return false;

    // Apro il file originale
    QFile inFile;
    inFile.setFileName(fileName);
    if (!inFile.open(QIODevice::ReadOnly))
        return false;

    // Apro il file risulato
    QuaZipFile outFile(zip);
    if (!outFile.open(QIODevice::WriteOnly, QuaZipNewInfo(fileDest, inFile.fileName()), password))
        return false;

    // Copio i dati
    if (!copyData(inFile, outFile) || outFile.getZipError() != UNZ_OK)
    {
        return false;
    }

    // Chiudo i file
    outFile.close();
    if (outFile.getZipError() != UNZ_OK)
        return false;
    inFile.close();

    return true;
}

bool JlCompressEx::compressDirEx(QString fileCompressed, QString dir, bool recursive, const char *password)
{
    return compressDirEx(fileCompressed, dir, recursive, QDir::Filters(), password);
}

bool JlCompressEx::compressDirEx(QString fileCompressed, QString dir, bool recursive, QDir::Filters filters, const char *password)
{
    // Creo lo zip
    QuaZip zip(fileCompressed);
    QDir().mkpath(QFileInfo(fileCompressed).absolutePath());
    if (!zip.open(QuaZip::mdCreate))
    {
        QFile::remove(fileCompressed);
        return false;
    }

    // Aggiungo i file e le sotto cartelle
    if (!compressSubDirEx(&zip, dir, dir, recursive, filters, password))
    {
        QFile::remove(fileCompressed);
        return false;
    }

    // Chiudo il file zip
    zip.close();
    if (zip.getZipError() != 0)
    {
        QFile::remove(fileCompressed);
        return false;
    }

    return true;
}

bool JlCompressEx::compressSubDirEx(QuaZip *zip, QString dir, QString origDir, bool recursive, QDir::Filters filters, const char *password)
{
    if (!zip)
        return false;
    if (zip->getMode() != QuaZip::mdCreate &&
        zip->getMode() != QuaZip::mdAppend &&
        zip->getMode() != QuaZip::mdAdd)
        return false;

    // Controllo la cartella
    QDir directory(dir);
    if (!directory.exists())
        return false;

    QDir origDirectory(origDir);
    if (dir != origDir)
    {
        QuaZipFile dirZipFile(zip);
        if (!dirZipFile.open(QIODevice::WriteOnly,
                             QuaZipNewInfo(origDirectory.relativeFilePath(dir) + QLatin1String("/"), dir), nullptr, 0, 0))
        {
            return false;
        }
        dirZipFile.close();
    }

    // Se comprimo anche le sotto cartelle
    if (recursive)
    {
        // Per ogni sotto cartella
        QFileInfoList files = directory.entryInfoList(QDir::AllDirs | QDir::NoDotAndDotDot | filters);
        for (const auto &file : files)
        {
            if (!file.isDir()) // needed for Qt < 4.7 because it doesn't understand AllDirs
                continue;
            // Comprimo la sotto cartella
            if (!compressSubDirEx(zip, file.absoluteFilePath(), origDir, recursive, filters, password))
                return false;
        }
    }

    // Per ogni file nella cartella
    QFileInfoList files = directory.entryInfoList(QDir::Files | filters);
    for (const auto &file : files)
    {
        // Se non e un file o e il file compresso che sto creando
        if (!file.isFile() || file.absoluteFilePath() == zip->getZipName())
            continue;

        // Creo il nome relativo da usare all'interno del file compresso
        QString filename = origDirectory.relativeFilePath(file.absoluteFilePath());

        // Comprimo il file
        if (!compressFileEx(zip, file.absoluteFilePath(), filename, password))
            return false;
    }
    return true;
}

QStringList JlCompressEx::extractDirEx(QString fileCompressed, QTextCodec *fileNameCodec, QString dir, const char *password)
{
    QuaZip zip(fileCompressed);
    if (fileNameCodec)
        zip.setFileNameCodec(fileNameCodec);
    return extractDirEx(zip, dir, password);
}

QStringList JlCompressEx::extractDirEx(QString fileCompressed, QString dir, const char *password)
{
    return extractDirEx(fileCompressed, nullptr, dir, password);
}

QStringList JlCompressEx::extractDirEx(QuaZip &zip, const QString &dir, const char *password)
{
    if (!zip.open(QuaZip::mdUnzip))
    {
        return QStringList();
    }
    QString cleanDir = QDir::cleanPath(dir);
    QDir directory(cleanDir);
    QString absCleanDir = directory.absolutePath();
    if (!absCleanDir.endsWith(QLatin1Char('/'))) // It only ends with / if it's the FS root.
        absCleanDir += QLatin1Char('/');
    QStringList extracted;
    if (!zip.goToFirstFile())
    {
        return QStringList();
    }
    do
    {
        QString name = zip.getCurrentFileName();
        QString absFilePath = directory.absoluteFilePath(name);
        QString absCleanPath = QDir::cleanPath(absFilePath);
        if (!absCleanPath.startsWith(absCleanDir))
            continue;
        if (!extractFileEx(&zip, QLatin1String(""), absFilePath, password))
        {
            removeFile(extracted);
            return QStringList();
        }
        extracted.append(absFilePath);
    } while (zip.goToNextFile());

    zip.close();
    if (zip.getZipError() != 0)
    {
        removeFile(extracted);
        return QStringList();
    }
    return extracted;
}

bool JlCompressEx::extractFileEx(QuaZip *zip, QString fileName, QString fileDest, const char *password)
{
    if (!zip)
    {
        qInfo() << "zip is null";
        return false;
    }
    if (zip->getMode() != QuaZip::mdUnzip)
    {
        qInfo() << "zip mode is not mdUnzip";
        return false;
    }
    // Apro il file compresso
    if (!fileName.isEmpty())
        zip->setCurrentFile(fileName);

    QuaZipFile inFile(zip);
    if (!inFile.open(QIODevice::ReadOnly, password) || inFile.getZipError() != UNZ_OK)
    {
        qInfo() << __LINE__ << "Failed to open file: " << fileName;
        qInfo() << __FILE__ << __func__ << __LINE__;
        qInfo() << __LINE__ << "inFile.getZipError()" << inFile.getZipError();

        return false;
    }

    // Controllo esistenza cartella file risultato
    QDir curDir;
    if (fileDest.endsWith(QLatin1String("/")))
    {
        if (!curDir.mkpath(fileDest))
        {
            qInfo() << "Failed to create directory: " << fileDest;
            return false;
        }
    }
    else
    {
        if (!curDir.mkpath(QFileInfo(fileDest).absolutePath()))
        {
            qInfo() << "Failed to create directory: " << QFileInfo(fileDest).absolutePath();
            return false;
        }
    }

    QuaZipFileInfo64 info;
    if (!zip->getCurrentFileInfo(&info))
    {
        qInfo() << "Failed to get file info";
        return false;
    }

    QFile::Permissions srcPerm = info.getPermissions();
    if (fileDest.endsWith(QLatin1String("/")) && QFileInfo(fileDest).isDir())
    {
        if (srcPerm != 0)
        {
            QFile(fileDest).setPermissions(srcPerm);
        }
        return true;
    }

    if (info.isSymbolicLink())
    {
        QString target = QFile::decodeName(inFile.readAll());
        return QFile::link(target, fileDest);
    }

    // Apro il file risultato
    QFile outFile;
    outFile.setFileName(fileDest);
    if (!outFile.open(QIODevice::WriteOnly))
    {
        qInfo() << __LINE__ << "Failed to open file: " << fileDest;
        qInfo() << __LINE__ << "password" << password;
        qInfo() << __LINE__ << "outFile.errorString()" << outFile.errorString();
        return false;
    }
    // Copio i dati
    if (!copyData(inFile, outFile) || inFile.getZipError() != UNZ_OK)
    {
        outFile.close();
        removeFile(QStringList(fileDest));
        qInfo() << "Failed to copy data";
        return false;
    }
    outFile.close();

    // Chiudo i file
    inFile.close();
    if (inFile.getZipError() != UNZ_OK)
    {
        removeFile(QStringList(fileDest));
        qInfo() << "Failed to close file";
        return false;
    }

    if (srcPerm != 0)
    {
        outFile.setPermissions(srcPerm);
    }
    return true;
}

bool JlCompressEx::CompressBuffer(QuaZip &zip, QByteArray &sourceData, QString fileName, const char *password)
{
    // Controllo l'apertura dello zip
    if (zip.getMode() != QuaZip::mdCreate &&
        zip.getMode() != QuaZip::mdAppend &&
        zip.getMode() != QuaZip::mdAdd)
    {
        return false;
    }
    // Apro il file risulato
    QuaZipFile outFile(&zip);
    if (!outFile.open(QIODevice::WriteOnly, QuaZipNewInfo(fileName), password))
    {
        return false;
    }
    // Copio i dati
    if (outFile.write(sourceData) != sourceData.size())
    {
        return false;
    }
    if (outFile.getZipError() != UNZ_OK)
    {
        return false;
    }

    // Chiudo i file
    outFile.close();
    if (outFile.getZipError() != UNZ_OK)
    {
        return false;
    }

    return true;
}

bool JlCompressEx::extractToBuffer(const QString &strZipFile, const char *password, QList<QPair<QString, QByteArray>> &filesList)
{
    QuaZip zip(strZipFile);
    if (!zip.open(QuaZip::mdUnzip))
    {
        qWarning() << "open zip file error:" << zip.getZipError() << ". file:" << strZipFile;
        return false;
    }
    for (bool more = zip.goToFirstFile(); more; more = zip.goToNextFile())
    {
        QuaZipFileInfo64 info;
        if (!zip.getCurrentFileInfo(&info))
        {
            continue;
        }
        qDebug() << "in zip: fileName:" << info.name << ", compressedSize:" << info.compressedSize << ", uncompressedSize:" << info.uncompressedSize;
        QString strFileName = info.name; // 如果为多层目录则包含目录路径

        if (0 == info.compressedSize || 0 == info.uncompressedSize)
        { // 文件夹也会遍历出来，大小为0
            continue;
        }

        // 读取文件数据
        QuaZipFile zipFile(&zip);
        if (!zipFile.open(QIODevice::ReadOnly, password))
        {
            qWarning() << "open file error:" << zipFile.getZipError() << ". file name:" << strFileName;
            continue;
        }
        if (zipFile.getZipError() != UNZ_OK)
        {
            qWarning() << "open file error:" << zipFile.getZipError();
            continue;
        }
        // qDebug() << "file size:" << zipFile.size() << " isOpen:" << zipFile.isOpen();
        // continue;
        QByteArray fileData;
        while (!zipFile.atEnd())
        {
            QByteArray dataTmp = zipFile.readAll();
            if (dataTmp.isEmpty())
            { // 当密码不正确时打开文件会成功，这里会读取失败
                zipFile.close();
                zip.close();
                return false;
            }
            if (zipFile.getZipError() != UNZ_OK)
            {
                zipFile.close();
                zip.close();
                return false;
            }
            fileData += dataTmp;
        }
        filesList.append(QPair<QString, QByteArray>(strFileName, fileData));

        zipFile.close();
    }
    //
    zip.close();
    if (zip.getZipError() != 0)
    {
    }
    return true;
}
