﻿/*
 * @Author: liquan
 * @Date: 2024-03-29 14:46:24
 * @Last Modified by: liquan
 * @Last Modified time: 2024-03-29 16:52:56
 */

#include <QDebug>
#include "JlCompressEx.h"
#include <QtZlib/zlib.h>
// #include "quazip/include/JlCompress.h"
#include "qzip.h"

QZip &QZip::instance()
{
    static QZip instance;
    return instance;
}

// filePath: 创建zip文件的路径。
// dirPath: 要压缩的目录的路径。
// password: 压缩文件的密码。
bool QZip::zip(const QString &zipName, const QString &fileName, const QString &password)
{
    // QString zipName = "F:/example/test.zip";
    // QString fileName = "F:/example/test.txt";
    return JlCompressEx::compressDirEx(zipName, fileName, true, password.toStdString().c_str());
    // JlCompress::compressDir(zipName, fileName);
}
QString getUniqueDirectoryName(const QString &basePath, const QString &dirName)
{
    // 检查原始目录名是否存在
    QFileInfo fileInfo(basePath + QDir::separator() + dirName);
    if (!fileInfo.exists())
    {
        // 如果不存在，直接返回原始目录名
        return dirName;
    }

    // 如果存在，开始添加编号以生成唯一名称
    int index = 1;
    QString newDirName;
    do
    {
        // 组装新的目录名，格式为“原始名称(编号)”
        newDirName = QString("%1(%2)").arg(dirName).arg(index);
        fileInfo.setFile(basePath + QDir::separator() + newDirName);
        index++; // 增加编号以便下次迭代
    } while (fileInfo.exists()); // 检查新名称是否已存在，直到找到一个不存在的名称

    return newDirName; // 返回生成的唯一目录名
}

// filePath: 解压的zip文件的路径。
// dirPath: 要解压的目录的路径。
QStringList QZip::unzip(const QString &filePath, const QString &dirPath, const QString &password)
{
/*     // 从zip文件的提取名字 例如F:/example/test.zip -> test
    QString fileName = QFileInfo(filePath).baseName();
    // 添加解压同名目录
    QString newfileName = getUniqueDirectoryName(dirPath, fileName);
    QString dirPathTemp = dirPath + QDir::separator() + newfileName;

    qInfo() << "newfileName: " << dirPathTemp; */
    // return JlCompressEx::extractDirEx(filePath, dirPathTemp, password.toUtf8().constData());
    return JlCompressEx::extractDirEx(filePath, dirPath, password.toUtf8().constData());
}
 
