/****************************************************************************
**
** https://www.qxorm.com/
** Copyright (C) 2013 <PERSON> (<EMAIL>)
**
** This file is part of the QxOrm library
**
** This software is provided 'as-is', without any express or implied
** warranty. In no event will the authors be held liable for any
** damages arising from the use of this software
**
** Commercial Usage
** Licensees holding valid commercial QxOrm licenses may use this file in
** accordance with the commercial license agreement provided with the
** Software or, alternatively, in accordance with the terms contained in
** a written agreement between you and <PERSON> Marty
**
** GNU General Public License Usage
** Alternatively, this file may be used under the terms of the GNU
** General Public License version 3.0 as published by the Free Software
** Foundation and appearing in the file 'license.gpl3.txt' included in the
** packaging of this file. Please review the following information to
** ensure the GNU General Public License version 3.0 requirements will be
** met : http://www.gnu.org/copyleft/gpl.html
**
** If you are unsure which license is appropriate for your use, or
** if you have questions regarding the use of this file, please contact :
** <EMAIL>
**
****************************************************************************/

#if (QT_VERSION < QT_VERSION_CHECK(6, 0, 0))
#ifdef _QX_ENABLE_BOOST_SERIALIZATION
#ifdef _QX_ENABLE_QT_GUI
#ifndef _QX_SERIALIZE_QMATRIX_H_
#define _QX_SERIALIZE_QMATRIX_H_

#ifdef _MSC_VER
#pragma once
#endif

#include <boost/serialization/serialization.hpp>
#include <boost/serialization/split_free.hpp>
#include <boost/serialization/nvp.hpp>

#include <QtGui/qmatrix.h>

#include <QxSerialize/QxSerializeFastCompil.h>

#include <QxRegister/QxVersion.h>

QX_CLASS_VERSION(QMatrix, 0)

QX_SERIALIZE_FAST_COMPIL_SAVE_LOAD_HPP(QX_DLL_EXPORT, QMatrix)

#endif // _QX_SERIALIZE_QMATRIX_H_
#endif // _QX_ENABLE_QT_GUI
#endif // _QX_ENABLE_BOOST_SERIALIZATION
#endif // (QT_VERSION < QT_VERSION_CHECK(6, 0, 0))
