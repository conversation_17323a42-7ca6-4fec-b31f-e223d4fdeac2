#ifndef _SQLORM_H_
#define _SQLORM_H_

#include "IDEV1_Common_global.h"
#include <QMap>
#include <QObject>
#include <QSqlDatabase>
#include <QString>

class IDEV1_COMMON_EXPORT SqlOrm : public QObject
{
    Q_OBJECT
  public:
    static SqlOrm &instance();
    static void initQxOrmSettings();                                    // 全局初始化QxOrm设置，关闭调试信息
    bool databaseCreate(const QString &name, const QString &password);  // 创建数据库
    bool databaseConnect(const QString &name, const QString &password); // 连接数据库
    QSqlDatabase &getDatabase(const QString &pathName);                 // 获取数据库实例
    void closeDatabase(const QString &pathName);                        // 关闭数据库
    void closeAllDatabase();                                            // 关闭所有数据库
    void closePrintInfo();                                              // 关闭QxOrm调试打印信息
  private:
    QMap<QString, QSqlDatabase> databaseMap;
};

#endif
