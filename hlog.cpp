#include "hlog.h"
#include <QDir>
#include <QFile>

Hlog::Hlog()
	: m_bInit(false)
{
}

Hlog::~Hlog()
{
	if (m_bInit)
	{
		this->UnInit();
	}
}

Hlog &Hlog::instance()
{
	static Hlog instance;
	return instance;
}

Hlog::OutMode Hlog::GetOutModeEnum(const std::string &strMode)
{
	OutMode eMode;
	if (strMode == "SYNC")
	{
		eMode = OutMode::SYNC;
	}
	else if (strMode == "ASYNC")
	{
		eMode = OutMode::ASYNC;
	}
	else
	{
		eMode = OutMode::SYNC;
	}

	return eMode;
}

Hlog::OutLevel Hlog::GetOutLevelEnum(const std::string &strLevel)
{
	OutLevel eLevel;

	if (strLevel == "TRACE")
	{
		eLevel = OutLevel::LEVEL_TRACE;
	}
	else if (strLevel == "DEBUG")
	{
		eLevel = OutLevel::LEVEL_DEBUG;
	}
	else if (strLevel == "INFO")
	{
		eLevel = OutLevel::LEVEL_INFO;
	}
	else if (strLevel == "WARN")
	{
		eLevel = OutLevel::LEVEL_WARN;
	}
	else if (strLevel == "ERROR")
	{
		eLevel = OutLevel::LEVEL_ERROR;
	}
	else if (strLevel == "CRITI")
	{
		eLevel = OutLevel::LEVEL_CRITI;
	}
	else
	{
		eLevel = OutLevel::LEVEL_TRACE;
	}

	return eLevel;
}

bool Hlog::AddColorConsole(const char *pLoggerName, const OutLevel level)
{
	// qDebug() << "[log]: AddColorConsole: logName = " << pLoggerName << "  level = " << level;
	auto console_sink = std::make_shared<spdlog::sinks::stdout_color_sink_mt>();
	console_sink->set_level((spdlog::level::level_enum)level);
	console_sink->set_pattern(LOG_OUTPUT_FORMAT);
	UpdateSinkMap(pLoggerName, console_sink);
	return true;
}

bool Hlog::AddRotatingFile(const char *pLoggerName, const char *pFileName, const int nMaxFileSize, const int nMaxFile, const OutLevel level)
{
	// qDebug() << "[log]: AddRotatingFile: logName = " << pLoggerName << "  level = " << level << "  fileName = " << pFileName << "  maxFileSize = " << nMaxFileSize << "  maxFile = " << nMaxFile;
	auto file_sink = std::make_shared<spdlog::sinks::rotating_file_sink_mt>(pFileName, nMaxFileSize, nMaxFile);
	file_sink->set_level((spdlog::level::level_enum)level);
	// file_sink->set_pattern(LOG_OUTPUT_FORMAT);
	UpdateSinkMap(pLoggerName, file_sink);
	return true;
}

bool Hlog::AddDailyFile(const char *pLoggerName, const char *pFileName, const int nHour, const int nMinute, const OutLevel eLevel)
{
	//"%Y-%m-%d:%H:%M:%S.log"
	// qDebug() << "[log]: AddDailyFile: logName = " << pLoggerName << "  level = " << eLevel << "  fileName = " << pFileName << "  hour = " << nHour << "  minute = " << nMinute;
	auto DailyFileSink = std::make_shared<spdlog::sinks::daily_file_sink_mt>(pFileName, nHour, nMinute);
	DailyFileSink->set_level((spdlog::level::level_enum)eLevel);
	UpdateSinkMap(pLoggerName, DailyFileSink);
	return true;
}

bool Hlog::InitConfig(const QString &pFileName)
{
	// qDebug() << "pFileName :" << QDir().currentPath() + pFileName;
	try
	{
		QFile file(QDir().currentPath() + pFileName);
		if (!file.open(QIODevice::ReadWrite))
		{
			qDebug() << QDir().currentPath() + pFileName;
			qDebug() << "Failed to open config file:" << pFileName;
			qDebug() << "Error:" << file.errorString(); // 打印错误信息
			return false;
		}
		QJsonDocument doc = QJsonDocument::fromJson(file.readAll());
		// qDebug() << "JSON parsing succeeded!" << doc;
		QJsonObject jsonRoot = doc.object();

		// 解析 JSON 数据
		QJsonArray loggers = jsonRoot["loggers"].toArray();
		QString strOutMode = jsonRoot["outputMode"].toString();
		QString strOutFormat = jsonRoot["outputFormat"].toString();
		OutMode eOutMode = GetOutModeEnum(strOutMode.toStdString());
		// qDebug() << "loggers:" << loggers;
		// qDebug() << "outputMode:" << strOutMode;
		// qDebug() << "outputFormat:" << strOutFormat;

		// 解析记录器配置
		for (const auto &loggerVal : loggers)
		{
			QJsonObject loggerObj = loggerVal.toObject();
			QString loggerName = loggerObj["name"].toString();
			QJsonArray jsSinks = loggerObj["sinks"].toArray();
			// qDebug() << "name:" << loggerName;
			// qDebug() << "sinks:" << jsSinks;

			// 遍历并解析每个 sink
			for (const auto &jsSink : jsSinks)
			{
				QJsonObject sinkObj = jsSink.toObject();
				QString type = sinkObj["type"].toString();
				QString level = sinkObj["level"].toString();

				// qDebug() << "type:" << type;
				// qDebug() << "level:" << level;

				Hlog::OutLevel eLevel = Hlog::GetOutLevelEnum(level.toStdString());
				if (type == "color_console") // 控制台
				{
					QString loggerName = loggerObj["name"].toString();
					AddColorConsole(loggerName.toStdString().c_str(), eLevel);
					// qDebug() << "AddColorConsole: loggerName = " << loggerName.toStdString().c_str() << "  level = " << eLevel;
				}
				else if (type == "rotating_file") // 回滚文件
				{
					QString fileName = sinkObj["fileName"].toString();
					int maxFileSize = sinkObj["maxFileSize"].toInt();
					int maxFile = sinkObj["maxFile"].toInt();
					AddRotatingFile(loggerName.toStdString().c_str(), fileName.toStdString().c_str(), maxFileSize, maxFile, eLevel);
				}
				else if (type == "daily_file") // 日期文件
				{
					QString fileName = sinkObj["fileName"].toString();
					int hour = sinkObj["hour"].toInt();
					int minute = sinkObj["minute"].toInt();
					AddDailyFile(loggerName.toStdString().c_str(), fileName.toStdString().c_str(), hour, minute, eLevel);
				}
			}
		}
		return Init(eOutMode, strOutFormat);
	}
	catch (const QJsonParseError &e)
	{
		qDebug() << "JSON parsing failed! Error:" << e.errorString();
		return false;
	}
	catch (const std::exception &e)
	{
		qDebug() << "InitConfig failed! Error:" << e.what();
		return false;
	}
}

bool Hlog::Init(const OutMode outMode, const QString &strLogFormat)
{
	// qDebug() << "[log]: Init: outMode = " << outMode << "  strLogFormat = " << strLogFormat.c_str();
	if (m_bInit)
	{
		qDebug() << "It's already initialized";
		return false;
	}
	m_bInit = true;
	if (outMode == ASYNC) // 异步
	{
		// qDebug() << "[log]: mode = ASYNC";
		for (auto e : m_mapLoggerParam)
		{
			std::string strLogName = e.first;
			std::vector<spdlog::sink_ptr> vecSink(e.second);
			auto tp = std::make_shared<spdlog::details::thread_pool>(1024000, 1);
			auto logger = std::make_shared<spdlog::async_logger>(strLogName, begin(vecSink), end(vecSink), tp, spdlog::async_overflow_policy::block);
			UpdateThreadPoolMap(strLogName, tp);
			// 设置根日志输出等级
			logger->set_level(spdlog::level::trace);
			// 遇到warn级别，立即flush到文件
			logger->flush_on(spdlog::level::warn);
			spdlog::register_logger(logger);
		}
	}
	else // 同步
	{
		// qDebug() << "[log]: mode = SYNC";
		for (auto e : m_mapLoggerParam)
		{
			std::string strLogName = e.first;
			std::vector<spdlog::sink_ptr> vecSink(e.second);
			auto logger = std::make_shared<spdlog::logger>(strLogName, begin(vecSink), end(vecSink));
			// 设置根日志输出等级
			logger->set_level(spdlog::level::trace);
			// 遇到warn级别，立即flush到文件
			logger->flush_on(spdlog::level::warn);
			spdlog::register_logger(logger);
		}
	}

	// 定时flush到文件，每三秒刷新一次
	spdlog::flush_every(std::chrono::seconds(3));
	// 设置全局记录器的输出格式
	spdlog::set_pattern(strLogFormat.toStdString());

	return true;
}

void Hlog::UnInit()
{
	spdlog::drop_all();
	spdlog::shutdown();
}

std::vector<std::string> Hlog::StringSplit(const std::string &strSrc, const std::string &strSplit)
{
	std::vector<std::string> resVec;
	if ("" == strSrc)
	{
		return resVec;
	}
	// 方便截取最后一段数据
	std::string strs = strSrc + strSplit;

	size_t pos = strs.find(strSplit);
	size_t size = strs.size();

	while (pos != std::string::npos)
	{
		std::string x = strs.substr(0, pos);
		resVec.push_back(x);
		strs = strs.substr(pos + 1, size);
		pos = strs.find(strSplit);
	}

	return resVec;
}

void Hlog::UpdateSinkMap(std::string strLoggerName, spdlog::sink_ptr pSink)
{
	auto iter = m_mapLoggerParam.find(strLoggerName);
	if (iter != m_mapLoggerParam.end())
	{
		iter->second.push_back(pSink);
	}
	else
	{
		std::vector<spdlog::sink_ptr> vecSink;
		vecSink.push_back(pSink);
		m_mapLoggerParam[strLoggerName] = vecSink;
	}
}

void Hlog::UpdateThreadPoolMap(std::string strLoggerName, std::shared_ptr<spdlog::details::thread_pool> pThreadPool)
{
	auto iter = m_mapAsyncThreadPool.find(strLoggerName);
	if (iter != m_mapAsyncThreadPool.end())
	{
		iter->second = (pThreadPool);
	}
	else
	{
		m_mapAsyncThreadPool[strLoggerName] = pThreadPool;
	}
}
