#VERSION = 2.0.0

QT -= gui
QT += sql printsupport
TARGET = IDECommon
TEMPLATE = lib
DEFINES += IDEV1_COMMON_LIBRARY
DEFINES += APP_VERSION=\\\"$$VERSION\\\"

CONFIG += c++17
# You can make your code fail to compile if it uses deprecated APIs.
# In order to do so, uncomment the following line.
#DEFINES += QT_DISABLE_DEPRECATED_BEFORE=0x060000    # disables all the APIs deprecated before Qt 6.0.0

SOURCES += \
    idev1_common.cpp \
    hlog.cpp \
    sqlorm.cpp \
    qzip.cpp \ 
    JlCompressEx.cpp 

HEADERS += \
    IDEV1_Common_global.h \
    idev1_common.h \
    hlog.h \
    sqlorm.h \
    qzip.h \
    JlCompressEx.h

#中文
RC_LANG = 0x0004
# 公司名
QMAKE_TARGET_COMPANY = Three Gorges Intelligent Industrial Control Technology
# 产品名称
QMAKE_TARGET_PRODUCT = ReliAUTO Studio
# 详细描述
QMAKE_TARGET_DESCRIPTION = C++ Application Development Framework
# 版权
QMAKE_TARGET_COPYRIGHT = Copyright(C) 2023-2043 Three Gorges Intelligent Industrial Control Technology

 
# Default rules for deployment.
unix {
    target.path = /usr/lib
}
!isEmpty(target.path): INSTALLS += target


# 目标文件夹路径
DEST_DIR = $$OUT_PWD/../idev1_mainapp/include/IDECommon/

# 转换目标路径中的斜杠（如果在 Windows 上）
win32:DEST_DIR = $$replace(DEST_DIR, /, \\)

# 初始化复制命令
COPY_COMMANDS =

CONFIG(release, debug|release) {
    # 定义要复制的文件路径
    TargetDll = $$OUT_PWD/release/*.dll
    TargetLib = $$OUT_PWD/release/*.lib
    Targethead = $$PWD/*.h

    # 将路径中的斜杠替换为反斜杠
    TargetDll = $$replace(TargetDll, /, \\)
    TargetLib = $$replace(TargetLib, /, \\)
    Targethead = $$replace(Targethead, /, \\)

    # 定义输出目录
    OutputDllDir1 = $$OUT_PWD/../IDEV1_MainApp/modules/IDECommon/
    OutputRUNDllDir1 = $$OUT_PWD/../build-IDEV1_MainApp-Desktop_Qt_5_15_2_MSVC2019_64bit-Release/
    OutputLibDir1 = $$OUT_PWD/../IDEV1_MainApp/lib/
    OutputHeadDir1 = $$OUT_PWD/../IDEV1_MainApp/include/IDECommon/

    OutputDllDir2 = $$OUT_PWD/../IDEV1_UserAndGroup/IDECommon/lib/
    OutputRUNDllDir2 = $$OUT_PWD/../build-IDEV1_UserAndGroup-Desktop_Qt_5_15_2_MSVC2019_64bit-Release/
    OutputLibDir2 = $$OUT_PWD/../IDEV1_UserAndGroup/IDECommon/lib/
    OutputHeadDir2 = $$OUT_PWD/../IDEV1_UserAndGroup/IDECommon/include/

    OutputDllDir3 = $$OUT_PWD/../idev1_projectandfile/IDECommon/lib/
    OutputRUNDllDir3 = $$OUT_PWD/../build-IDEV1_ProjectAndFile-Desktop_Qt_5_15_2_MSVC2019_64bit-Release/
    OutputLibDir3 = $$OUT_PWD/../idev1_projectandfile/IDECommon/lib/
    OutputHeadDir3 = $$OUT_PWD/../idev1_projectandfile/IDECommon/include/

    OutputDllDir4 = $$OUT_PWD/../idev1_deviceandnetwork/IDECommon/lib/
    OutputRUNDllDir4 = $$OUT_PWD/../build-IDEV1_DeviceAndNetwork-Desktop_Qt_5_15_2_MSVC2019_64bit-Release/
    OutputLibDir4 = $$OUT_PWD/../idev1_deviceandnetwork/IDECommon/lib/
    OutputHeadDir4 = $$OUT_PWD/../idev1_deviceandnetwork/IDECommon/include/    

    OutputDllDir5 = $$OUT_PWD/../idev1_variable/IDECommon/lib/
    OutputRUNDllDir5 = $$OUT_PWD/../build-IDEV1_Variable-Desktop_Qt_5_15_2_MSVC2019_64bit-Release/
    OutputLibDir5 = $$OUT_PWD/../idev1_variable/IDECommon/lib/
    OutputHeadDir5 = $$OUT_PWD/../idev1_variable/IDECommon/include/    

    OutputDllDir6 = $$OUT_PWD/../idev1_canopen/IDECommon/lib/
    OutputRUNDllDir6 = $$OUT_PWD/../build-IDEV1_Canopen-Desktop_Qt_5_15_2_MSVC2019_64bit-Release/
    OutputLibDir6 = $$OUT_PWD/../idev1_canopen/IDECommon/lib/
    OutputHeadDir6 = $$OUT_PWD/../idev1_canopen/IDECommon/include/

    OutputDllDir7 = $$OUT_PWD/../idev1_ldeditor/IDECommon/lib/
    OutputRUNDllDir7 = $$OUT_PWD/../build-IDEV1_LDEditor-Desktop_Qt_5_15_2_MSVC2019_64bit-Release/
    OutputLibDir7 = $$OUT_PWD/../idev1_ldeditor/IDECommon/lib/
    OutputHeadDir7 = $$OUT_PWD/../idev1_ldeditor/IDECommon/include/


    # 更新路径斜杠
    OutputDllDir1 = $$replace(OutputDllDir1, /, \\)
    OutputRUNDllDir1 = $$replace(OutputRUNDllDir1, /, \\)
    OutputLibDir1 = $$replace(OutputLibDir1, /, \\)
    OutputHeadDir1 = $$replace(OutputHeadDir1, /, \\)

    OutputDllDir2 = $$replace(OutputDllDir2, /, \\)
    OutputRUNDllDir2 = $$replace(OutputRUNDllDir2, /, \\)
    OutputLibDir2 = $$replace(OutputLibDir2, /, \\)
    OutputHeadDir2 = $$replace(OutputHeadDir2, /, \\)

    OutputDllDir3 = $$replace(OutputDllDir3, /, \\)
    OutputRUNDllDir3 = $$replace(OutputRUNDllDir3, /, \\)
    OutputLibDir3 = $$replace(OutputLibDir3, /, \\)
    OutputHeadDir3 = $$replace(OutputHeadDir3, /, \\)

    OutputDllDir4 = $$replace(OutputDllDir4, /, \\)
    OutputRUNDllDir4 = $$replace(OutputRUNDllDir4, /, \\)
    OutputLibDir4 = $$replace(OutputLibDir4, /, \\)
    OutputHeadDir4 = $$replace(OutputHeadDir4, /, \\)

    OutputDllDir5 = $$replace(OutputDllDir5, /, \\)
    OutputRUNDllDir5 = $$replace(OutputRUNDllDir5, /, \\)
    OutputLibDir5 = $$replace(OutputLibDir5, /, \\)
    OutputHeadDir5 = $$replace(OutputHeadDir5, /, \\)

    OutputDllDir6 = $$replace(OutputDllDir6, /, \\)
    OutputRUNDllDir6 = $$replace(OutputRUNDllDir6, /, \\)
    OutputLibDir6 = $$replace(OutputLibDir6, /, \\)
    OutputHeadDir6 = $$replace(OutputHeadDir6, /, \\)

    OutputDllDir7 = $$replace(OutputDllDir7, /, \\)
    OutputRUNDllDir7 = $$replace(OutputRUNDllDir7, /, \\)
    OutputLibDir7 = $$replace(OutputLibDir7, /, \\)
    OutputHeadDir7 = $$replace(OutputHeadDir7, /, \\)

    # 复制命令
    win32 {
        QMAKE_POST_LINK +=  copy /Y $$TargetDll $$OutputDllDir1 && \
                            copy /Y $$TargetDll $$OutputRUNDllDir1 && \
                            copy /Y $$TargetLib $$OutputLibDir1 && \
                            copy /Y $$Targethead $$OutputHeadDir1 && \
                            copy /Y $$TargetDll $$OutputDllDir2 && \
                            copy /Y $$TargetDll $$OutputRUNDllDir2 && \
                            copy /Y $$TargetLib $$OutputLibDir2 && \
                            copy /Y $$Targethead $$OutputHeadDir2 && \
                            copy /Y $$TargetDll $$OutputDllDir3 && \
                            copy /Y $$TargetDll $$OutputRUNDllDir3 && \
                            copy /Y $$TargetLib $$OutputLibDir3 && \
                            copy /Y $$Targethead $$OutputHeadDir3 && \
                            copy /Y $$TargetDll $$OutputDllDir4 && \
                            copy /Y $$TargetDll $$OutputRUNDllDir4 && \
                            copy /Y $$TargetLib $$OutputLibDir4 && \
                            copy /Y $$Targethead $$OutputHeadDir4 && \
                            copy /Y $$TargetDll $$OutputDllDir5 && \
                            copy /Y $$TargetDll $$OutputRUNDllDir5 && \
                            copy /Y $$TargetLib $$OutputLibDir5 && \
                            copy /Y $$Targethead $$OutputHeadDir5 && \
                            copy /Y $$TargetDll $$OutputDllDir6 && \
                            copy /Y $$TargetDll $$OutputRUNDllDir6 && \
                            copy /Y $$TargetLib $$OutputLibDir6 && \
                            copy /Y $$Targethead $$OutputHeadDir6 && \
                            copy /Y $$TargetDll $$OutputDllDir7 && \
                            copy /Y $$TargetDll $$OutputRUNDllDir7 && \
                            copy /Y $$TargetLib $$OutputLibDir7 && \
                            copy /Y $$Targethead $$OutputHeadDir7
    }
    unix {
        QMAKE_POST_LINK +=  cp -f $$TargetDll $$OutputDllDir1 && \
                            cp -f $$TargetDll $$OutputRUNDllDir1 && \
                            cp -f $$TargetLib $$OutputLibDir1 && \
                            cp -f $$Targethead $$OutputHeadDir1 && \
                            cp -f $$TargetDll $$OutputDllDir2 && \
                            cp -f $$TargetDll $$OutputRUNDllDir2 && \
                            cp -f $$TargetLib $$OutputLibDir2 && \
                            cp -f $$Targethead $$OutputHeadDir2 && \
                            cp -f $$TargetDll $$OutputDllDir3 && \
                            cp -f $$TargetDll $$OutputRUNDllDir3 && \
                            cp -f $$TargetLib $$OutputLibDir3 && \
                            cp -f $$Targethead $$OutputHeadDir3 && \
                            cp -f $$TargetDll $$OutputDllDir4 && \
                            cp -f $$TargetDll $$OutputRUNDllDir4 && \
                            cp -f $$TargetLib $$OutputLibDir4 && \
                            cp -f $$Targethead $$OutputHeadDir4 && \
                            cp -f $$TargetDll $$OutputDllDir5 && \
                            cp -f $$TargetDll $$OutputRUNDllDir5 && \
                            cp -f $$TargetLib $$OutputLibDir5 && \
                            cp -f $$Targethead $$OutputHeadDir5 && \
                            cp -f $$TargetDll $$OutputDllDir6 && \
                            cp -f $$TargetDll $$OutputRUNDllDir6 && \
                            cp -f $$TargetLib $$OutputLibDir6 && \
                            cp -f $$Targethead $$OutputHeadDir6 && \
                            cp -f $$TargetDll $$OutputDllDir7 && \
                            cp -f $$TargetDll $$OutputRUNDllDir7 && \
                            cp -f $$TargetLib $$OutputLibDir7 && \
                            cp -f $$Targethead $$OutputHeadDir7
    }
}

INCLUDEPATH += "$$[QT_INSTALL_HEADERS]/QtZlib"

#qxorm库的静态编译宏
DEFINES += _QX_BUILDING_QX_ORM _QX_STATIC_BUILD

unix:!macx|win32: LIBS += -L$$PWD/QxOrm/lib/ -lQxOrm
INCLUDEPATH += $$PWD/QxOrm/include
DEPENDPATH += $$PWD/QxOrm/include
win32:!win32-g++: PRE_TARGETDEPS += $$PWD/QxOrm/lib/QxOrm.lib
else:unix:!macx|win32-g++: PRE_TARGETDEPS += $$PWD/QxOrm/lib/libQxOrm.a

unix:!macx|win32: LIBS += -L$$PWD/spdlog/lib/ -lspdlog
INCLUDEPATH += $$PWD/spdlog/include
DEPENDPATH += $$PWD/spdlog/include
win32:!win32-g++: PRE_TARGETDEPS += $$PWD/spdlog/lib/spdlog.lib
else:unix:!macx|win32-g++: PRE_TARGETDEPS += $$PWD/spdlog/lib/libspdlog.a

win32: LIBS += -L$$PWD/quazip/lib/ -lquazip1-qt5

INCLUDEPATH += $$PWD/quazip/include
DEPENDPATH += $$PWD/quazip/include

unix:!macx|win32: LIBS += -L$$PWD/QXlsx/lib/ -lQXlsx

INCLUDEPATH += $$PWD/QXlsx/include
DEPENDPATH += $$PWD/QXlsx/include

win32:!win32-g++: PRE_TARGETDEPS += $$PWD/QXlsx/lib/QXlsx.lib
else:unix:!macx|win32-g++: PRE_TARGETDEPS += $$PWD/QXlsx/lib/libQXlsx.a
