/****************************************************************************
**
** https://www.qxorm.com/
** Copyright (C) 2013 <PERSON> (<EMAIL>)
**
** This file is part of the QxOrm library
**
** This software is provided 'as-is', without any express or implied
** warranty. In no event will the authors be held liable for any
** damages arising from the use of this software
**
** Commercial Usage
** Licensees holding valid commercial QxOrm licenses may use this file in
** accordance with the commercial license agreement provided with the
** Software or, alternatively, in accordance with the terms contained in
** a written agreement between you and <PERSON> Marty
**
** GNU General Public License Usage
** Alternatively, this file may be used under the terms of the GNU
** General Public License version 3.0 as published by the Free Software
** Foundation and appearing in the file 'license.gpl3.txt' included in the
** packaging of this file. Please review the following information to
** ensure the GNU General Public License version 3.0 requirements will be
** met : http://www.gnu.org/copyleft/gpl.html
**
** If you are unsure which license is appropriate for your use, or
** if you have questions regarding the use of this file, please contact :
** <EMAIL>
**
****************************************************************************/

#ifndef _QX_NO_JSON
#ifndef _QX_SERIALIZE_QJSON_ALL_INCLUDE_H_
#define _QX_SERIALIZE_QJSON_ALL_INCLUDE_H_

#ifdef _MSC_VER
#pragma once
#endif

/*!
 * \file QxSerializeQJson_all_include.h
 * \author Lionel Marty
 * \ingroup QxSerialize
 * \brief Include all Qt QJson serialization method (save/load) provided by QxOrm library
 */

#ifdef _QX_ENABLE_BOOST
#include <QxSerialize/QJson/QxSerializeQJson_boost_scoped_ptr.h>
#include <QxSerialize/QJson/QxSerializeQJson_boost_shared_ptr.h>
#include <QxSerialize/QJson/QxSerializeQJson_boost_tuple.h>
#include <QxSerialize/QJson/QxSerializeQJson_boost_unordered_map.h>
#include <QxSerialize/QJson/QxSerializeQJson_boost_unordered_set.h>
#endif // _QX_ENABLE_BOOST

#include <QxSerialize/QJson/QxSerializeQJson_QBrush.h>
#include <QxSerialize/QJson/QxSerializeQJson_QColor.h>
#include <QxSerialize/QJson/QxSerializeQJson_QFlags.h>
#include <QxSerialize/QJson/QxSerializeQJson_QFont.h>
#include <QxSerialize/QJson/QxSerializeQJson_QHash.h>
#include <QxSerialize/QJson/QxSerializeQJson_QImage.h>
#include <QxSerialize/QJson/QxSerializeQJson_QLinkedList.h>
#include <QxSerialize/QJson/QxSerializeQJson_QList.h>
#include <QxSerialize/QJson/QxSerializeQJson_QMap.h>
#include <QxSerialize/QJson/QxSerializeQJson_QMatrix.h>
#include <QxSerialize/QJson/QxSerializeQJson_QMultiHash.h>
#include <QxSerialize/QJson/QxSerializeQJson_QMultiMap.h>
#include <QxSerialize/QJson/QxSerializeQJson_QObject.h>
#include <QxSerialize/QJson/QxSerializeQJson_QPair.h>
#include <QxSerialize/QJson/QxSerializeQJson_QPicture.h>
#include <QxSerialize/QJson/QxSerializeQJson_QPixmap.h>
#include <QxSerialize/QJson/QxSerializeQJson_QPoint.h>
#include <QxSerialize/QJson/QxSerializeQJson_QRect.h>
#include <QxSerialize/QJson/QxSerializeQJson_QRegExp.h>
#include <QxSerialize/QJson/QxSerializeQJson_QRegion.h>
#include <QxSerialize/QJson/QxSerializeQJson_QScopedPointer.h>
#include <QxSerialize/QJson/QxSerializeQJson_QSharedPointer.h>
#include <QxSerialize/QJson/QxSerializeQJson_QSize.h>
#include <QxSerialize/QJson/QxSerializeQJson_QSqlError.h>
#include <QxSerialize/QJson/QxSerializeQJson_QStringList.h>
#include <QxSerialize/QJson/QxSerializeQJson_QUrl.h>
#include <QxSerialize/QJson/QxSerializeQJson_QVector.h>
#include <QxSerialize/QJson/QxSerializeQJson_QWeakPointer.h>
#include <QxSerialize/QJson/QxSerializeQJson_QVariantHash.h>
#include <QxSerialize/QJson/QxSerializeQJson_QVariantMap.h>
#include <QxSerialize/QJson/QxSerializeQJson_std_list.h>
#include <QxSerialize/QJson/QxSerializeQJson_std_map.h>
#include <QxSerialize/QJson/QxSerializeQJson_std_pair.h>
#include <QxSerialize/QJson/QxSerializeQJson_std_set.h>
#include <QxSerialize/QJson/QxSerializeQJson_std_shared_ptr.h>
#include <QxSerialize/QJson/QxSerializeQJson_std_tuple.h>
#include <QxSerialize/QJson/QxSerializeQJson_std_unique_ptr.h>
#include <QxSerialize/QJson/QxSerializeQJson_std_unordered_map.h>
#include <QxSerialize/QJson/QxSerializeQJson_std_unordered_set.h>
#include <QxSerialize/QJson/QxSerializeQJson_std_vector.h>
#include <QxSerialize/QJson/QxSerializeQJson_qx_registered_class.h>
#include <QxSerialize/QJson/QxSerializeQJson_IxParameter.h>
#include <QxSerialize/QJson/QxSerializeQJson_IxService.h>
#include <QxSerialize/QJson/QxSerializeQJson_IxSqlElement.h>
#include <QxSerialize/QJson/QxSerializeQJson_IxPersistable.h>
#include <QxSerialize/QJson/QxSerializeQJson_QxCollection.h>
#include <QxSerialize/QJson/QxSerializeQJson_QxDaoPointer.h>
#include <QxSerialize/QJson/QxSerializeQJson_QxInvalidValue.h>
#include <QxSerialize/QJson/QxSerializeQJson_QxInvalidValueX.h>
#include <QxSerialize/QJson/QxSerializeQJson_QxSqlQuery.h>
#include <QxSerialize/QJson/QxSerializeQJson_QxTransaction.h>

#endif // _QX_SERIALIZE_QJSON_ALL_INCLUDE_H_
#endif // _QX_NO_JSON
