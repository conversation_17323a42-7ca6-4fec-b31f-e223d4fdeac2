/****************************************************************************
**
** https://www.qxorm.com/
** Copyright (C) 2013 <PERSON> (<EMAIL>)
**
** This file is part of the QxOrm library
**
** This software is provided 'as-is', without any express or implied
** warranty. In no event will the authors be held liable for any
** damages arising from the use of this software
**
** Commercial Usage
** Licensees holding valid commercial QxOrm licenses may use this file in
** accordance with the commercial license agreement provided with the
** Software or, alternatively, in accordance with the terms contained in
** a written agreement between you and <PERSON> Marty
**
** GNU General Public License Usage
** Alternatively, this file may be used under the terms of the GNU
** General Public License version 3.0 as published by the Free Software
** Foundation and appearing in the file 'license.gpl3.txt' included in the
** packaging of this file. Please review the following information to
** ensure the GNU General Public License version 3.0 requirements will be
** met : http://www.gnu.org/copyleft/gpl.html
**
** If you are unsure which license is appropriate for your use, or
** if you have questions regarding the use of this file, please contact :
** <EMAIL>
**
****************************************************************************/

#ifndef _QX_SERIALIZE_QDATASTREAM_ALL_INCLUDE_H_
#define _QX_SERIALIZE_QDATASTREAM_ALL_INCLUDE_H_

#ifdef _MSC_VER
#pragma once
#endif

/*!
 * \file QxSerializeQDataStream_all_include.h
 * \author Lionel Marty
 * \ingroup QxSerialize
 * \brief Include all Qt QDataStream serialization method (save/load) provided by QxOrm library
 */

#include <QxSerialize/QDataStream/QxSerializeQDataStream_boost_optional.h>
#include <QxSerialize/QDataStream/QxSerializeQDataStream_boost_scoped_ptr.h>
#include <QxSerialize/QDataStream/QxSerializeQDataStream_boost_shared_ptr.h>
#include <QxSerialize/QDataStream/QxSerializeQDataStream_boost_tuple.h>
#include <QxSerialize/QDataStream/QxSerializeQDataStream_boost_unordered_map.h>
#include <QxSerialize/QDataStream/QxSerializeQDataStream_boost_unordered_set.h>
#include <QxSerialize/QDataStream/QxSerializeQDataStream_primitive_type.h>
#include <QxSerialize/QDataStream/QxSerializeQDataStream_QFlags.h>
#include <QxSerialize/QDataStream/QxSerializeQDataStream_QObject.h>
#include <QxSerialize/QDataStream/QxSerializeQDataStream_QScopedPointer.h>
#include <QxSerialize/QDataStream/QxSerializeQDataStream_QSharedPointer.h>
#include <QxSerialize/QDataStream/QxSerializeQDataStream_QSqlError.h>
#include <QxSerialize/QDataStream/QxSerializeQDataStream_QWeakPointer.h>
#include <QxSerialize/QDataStream/QxSerializeQDataStream_std_list.h>
#include <QxSerialize/QDataStream/QxSerializeQDataStream_std_map.h>
#include <QxSerialize/QDataStream/QxSerializeQDataStream_std_pair.h>
#include <QxSerialize/QDataStream/QxSerializeQDataStream_std_set.h>
#include <QxSerialize/QDataStream/QxSerializeQDataStream_std_shared_ptr.h>
#include <QxSerialize/QDataStream/QxSerializeQDataStream_std_string.h>
#include <QxSerialize/QDataStream/QxSerializeQDataStream_std_tuple.h>
#include <QxSerialize/QDataStream/QxSerializeQDataStream_std_unique_ptr.h>
#include <QxSerialize/QDataStream/QxSerializeQDataStream_std_unordered_map.h>
#include <QxSerialize/QDataStream/QxSerializeQDataStream_std_unordered_set.h>
#include <QxSerialize/QDataStream/QxSerializeQDataStream_std_vector.h>
#include <QxSerialize/QDataStream/QxSerializeQDataStream_qx_registered_class.h>

#endif // _QX_SERIALIZE_QDATASTREAM_ALL_INCLUDE_H_
