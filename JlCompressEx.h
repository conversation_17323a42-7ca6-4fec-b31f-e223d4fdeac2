/*
 * @Author: liquan
 * @Date: 2024-03-29 14:46:20
 * @Last Modified by: liquan
 * @Last Modified time: 2024-03-29 16:52:42
 */

#ifndef JLCOMPRESSEX_H
#define JLCOMPRESSEX_H

#include <QtZlib/zlib.h>
#include <QBuffer>
#include <QByteArray>
#include "quazip/include/JlCompress.h"

class JlCompressEx : public JlCompress
{
public:
  // 文件压缩到内存buffer，zipIoDevice可以使用QBuffer zipBuffer;
  static bool CompressToBuffer(QString file, const char *password, QIODevice &zipIoDevice);
  // 内存数据压缩到内存buffer
  static bool CompressToBuffer(QByteArray &sourceData, QString fileName, const char *password, QIODevice &zipIoDevice);
  // 文件压缩到内存buffer
  static bool CompressToBuffer(QStringList files, const char *password, QIODevice &zipIoDevice);

  static bool compressFileEx(QuaZip *zip, QString fileName, QString fileDest, const char *password);

  static bool compressDirEx(QString fileCompressed, QString dir, bool recursive, const char *password);

  static bool compressDirEx(QString fileCompressed, QString dir, bool recursive, QDir::Filters filters, const char *password);
 
  static bool compressSubDirEx(QuaZip *zip, QString dir, QString origDir, bool recursive, QDir::Filters filters, const char *password);

  static QStringList extractDirEx(QString fileCompressed, QString dir, const char *password);

  static QStringList extractDirEx(QString fileCompressed, QTextCodec *fileNameCodec, QString dir, const char *password);

  static QStringList extractDirEx(QuaZip &zip, const QString &dir, const char *password);

  static bool extractFileEx(QuaZip *zip, QString fileName, QString fileDest, const char *password);

public:
  // 压缩数据
  static bool CompressBuffer(QuaZip &zip, QByteArray &sourceData, QString fileName, const char *password);

public:
  // 解压，解压出来所有文件数据保存在filesList<文件名, 文件内容>
  static bool extractToBuffer(const QString &strZipFile, const char *password, QList<QPair<QString, QByteArray>> &filesList);
};

#endif