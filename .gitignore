﻿CMakeLists.txt.user
/build
/test
/.vscode
/.vs
# In repository we don't need to have:
# Compiled object files
*.o

# Generated MOC, resource and UI files
moc_*.cpp
qrc_*.cpp
ui_*.h

# Debug and Release directories (created under Windows, not Linux)
Debug/
Release/

# .log files (usually created by QtTest - thanks to VestniK)
*.log

# Built windows .exe and linux binaries
# NOTE: PROJECT is a your project's name, analog of PROJECT.exe in Linux
*.exe
# *.dll
PROJECT

# Windows-specific files
Thumbs.db
desktop.ini
# Mac-specific things (thanks to <PERSON>)
.DS_Store

# Editors temporary files
*~
/IDEV1_Common.pro.user
/*.vcxproj
/*.filters
/*.user
/*.stash
/*.autosave
/*.qtvscr
/x64